#include "MemoryReaderGUI.h"
#include <iostream>
#include <vector>
#include <string>
#include <windows.h>
#include <tlhelp32.h>
#include <vcclr.h>

using namespace msclr::interop;

void MemoryReaderForm::InitializeComponent(void)
{
    this->groupBoxDriver = (gcnew System::Windows::Forms::GroupBox());
    this->btnInitDriver = (gcnew System::Windows::Forms::Button());
    this->lblDriverStatus = (gcnew System::Windows::Forms::Label());

    this->groupBoxProcess = (gcnew System::Windows::Forms::GroupBox());
    this->comboBoxProcesses = (gcnew System::Windows::Forms::ComboBox());
    this->btnRefreshProcesses = (gcnew System::Windows::Forms::Button());
    this->lblProcessInfo = (gcnew System::Windows::Forms::Label());
    this->txtProcessFilter = (gcnew System::Windows::Forms::TextBox());
    this->lblProcessFilter = (gcnew System::Windows::Forms::Label());

    this->groupBoxModules = (gcnew System::Windows::Forms::GroupBox());
    this->listBoxModules = (gcnew System::Windows::Forms::ListBox());
    this->btnRefreshModules = (gcnew System::Windows::Forms::Button());

    this->groupBoxMemory = (gcnew System::Windows::Forms::GroupBox());
    this->txtAddress = (gcnew System::Windows::Forms::TextBox());
    this->numSize = (gcnew System::Windows::Forms::NumericUpDown());
    this->btnReadMemory = (gcnew System::Windows::Forms::Button());
    this->lblAddress = (gcnew System::Windows::Forms::Label());
    this->lblSize = (gcnew System::Windows::Forms::Label());

    this->groupBoxOutput = (gcnew System::Windows::Forms::GroupBox());
    this->comboBoxDataType = (gcnew System::Windows::Forms::ComboBox());
    this->txtOutput = (gcnew System::Windows::Forms::TextBox());
    this->btnCopyOutput = (gcnew System::Windows::Forms::Button());

    this->statusStrip = (gcnew System::Windows::Forms::StatusStrip());
    this->statusLabel = (gcnew System::Windows::Forms::ToolStripStatusLabel());

    this->SuspendLayout();

    // Form
    this->AutoScaleDimensions = System::Drawing::SizeF(6, 13);
    this->AutoScaleMode = System::Windows::Forms::AutoScaleMode::Font;
    this->ClientSize = System::Drawing::Size(800, 600);
    this->Text = L"Memory Reader GUI - wnbios_poc";
    this->StartPosition = FormStartPosition::CenterScreen;

    // Driver GroupBox
    this->groupBoxDriver->Location = System::Drawing::Point(12, 12);
    this->groupBoxDriver->Size = System::Drawing::Size(380, 80);
    this->groupBoxDriver->Text = L"Driver Control";
    this->groupBoxDriver->TabIndex = 0;

    this->btnInitDriver->Location = System::Drawing::Point(15, 25);
    this->btnInitDriver->Size = System::Drawing::Size(120, 30);
    this->btnInitDriver->Text = L"Initialize Driver";
    this->btnInitDriver->UseVisualStyleBackColor = true;
    this->btnInitDriver->Click += gcnew System::EventHandler(this, &MemoryReaderForm::btnInitDriver_Click);

    this->lblDriverStatus->Location = System::Drawing::Point(150, 25);
    this->lblDriverStatus->Size = System::Drawing::Size(220, 30);
    this->lblDriverStatus->Text = L"Driver not initialized";
    this->lblDriverStatus->ForeColor = System::Drawing::Color::Red;
    this->lblDriverStatus->TextAlign = ContentAlignment::MiddleLeft;

    this->groupBoxDriver->Controls->Add(this->btnInitDriver);
    this->groupBoxDriver->Controls->Add(this->lblDriverStatus);

    // Process GroupBox
    this->groupBoxProcess->Location = System::Drawing::Point(12, 100);
    this->groupBoxProcess->Size = System::Drawing::Size(380, 150);
    this->groupBoxProcess->Text = L"Process Selection";
    this->groupBoxProcess->TabIndex = 1;

    this->lblProcessFilter->Location = System::Drawing::Point(15, 25);
    this->lblProcessFilter->Size = System::Drawing::Size(50, 20);
    this->lblProcessFilter->Text = L"Filter:";

    this->txtProcessFilter->Location = System::Drawing::Point(70, 23);
    this->txtProcessFilter->Size = System::Drawing::Size(195, 25);
    this->txtProcessFilter->TextChanged += gcnew System::EventHandler(this, &MemoryReaderForm::txtProcessFilter_TextChanged);

    this->comboBoxProcesses->Location = System::Drawing::Point(15, 55);
    this->comboBoxProcesses->Size = System::Drawing::Size(250, 25);
    this->comboBoxProcesses->DropDownStyle = ComboBoxStyle::DropDownList;
    this->comboBoxProcesses->SelectedIndexChanged += gcnew System::EventHandler(this, &MemoryReaderForm::comboBoxProcesses_SelectedIndexChanged);

    this->btnRefreshProcesses->Location = System::Drawing::Point(275, 53);
    this->btnRefreshProcesses->Size = System::Drawing::Size(90, 30);
    this->btnRefreshProcesses->Text = L"Refresh";
    this->btnRefreshProcesses->UseVisualStyleBackColor = true;
    this->btnRefreshProcesses->Click += gcnew System::EventHandler(this, &MemoryReaderForm::btnRefreshProcesses_Click);

    this->lblProcessInfo->Location = System::Drawing::Point(15, 90);
    this->lblProcessInfo->Size = System::Drawing::Size(350, 50);
    this->lblProcessInfo->Text = L"No process selected";

    this->groupBoxProcess->Controls->Add(this->lblProcessFilter);
    this->groupBoxProcess->Controls->Add(this->txtProcessFilter);
    this->groupBoxProcess->Controls->Add(this->comboBoxProcesses);
    this->groupBoxProcess->Controls->Add(this->btnRefreshProcesses);
    this->groupBoxProcess->Controls->Add(this->lblProcessInfo);

    // Modules GroupBox
    this->groupBoxModules->Location = System::Drawing::Point(408, 100);
    this->groupBoxModules->Size = System::Drawing::Size(380, 200);
    this->groupBoxModules->Text = L"Module List";
    this->groupBoxModules->TabIndex = 2;

    this->listBoxModules->Location = System::Drawing::Point(15, 25);
    this->listBoxModules->Size = System::Drawing::Size(250, 140);
    this->listBoxModules->Enabled = false;
    this->listBoxModules->SelectedIndexChanged += gcnew System::EventHandler(this, &MemoryReaderForm::listBoxModules_SelectedIndexChanged);

    this->btnRefreshModules->Location = System::Drawing::Point(275, 25);
    this->btnRefreshModules->Size = System::Drawing::Size(90, 30);
    this->btnRefreshModules->Text = L"Refresh";
    this->btnRefreshModules->Enabled = false;
    this->btnRefreshModules->UseVisualStyleBackColor = true;
    this->btnRefreshModules->Click += gcnew System::EventHandler(this, &MemoryReaderForm::btnRefreshModules_Click);

    this->groupBoxModules->Controls->Add(this->listBoxModules);
    this->groupBoxModules->Controls->Add(this->btnRefreshModules);

    // Memory GroupBox
    this->groupBoxMemory->Location = System::Drawing::Point(12, 260);
    this->groupBoxMemory->Size = System::Drawing::Size(380, 120);
    this->groupBoxMemory->Text = L"Memory Reading";
    this->groupBoxMemory->TabIndex = 3;

    this->lblAddress->Location = System::Drawing::Point(15, 25);
    this->lblAddress->Size = System::Drawing::Size(60, 20);
    this->lblAddress->Text = L"Address:";

    this->txtAddress->Location = System::Drawing::Point(80, 23);
    this->txtAddress->Size = System::Drawing::Size(150, 25);
    this->txtAddress->Text = L"0x";

    this->lblSize->Location = System::Drawing::Point(250, 25);
    this->lblSize->Size = System::Drawing::Size(35, 20);
    this->lblSize->Text = L"Size:";

    this->numSize->Location = System::Drawing::Point(290, 23);
    this->numSize->Size = System::Drawing::Size(75, 25);
    this->numSize->Minimum = 1;
    this->numSize->Maximum = 1024;
    this->numSize->Value = 8;

    this->btnReadMemory->Location = System::Drawing::Point(15, 60);
    this->btnReadMemory->Size = System::Drawing::Size(100, 30);
    this->btnReadMemory->Text = L"Read Memory";
    this->btnReadMemory->UseVisualStyleBackColor = true;
    this->btnReadMemory->Click += gcnew System::EventHandler(this, &MemoryReaderForm::btnReadMemory_Click);

    this->groupBoxMemory->Controls->Add(this->lblAddress);
    this->groupBoxMemory->Controls->Add(this->txtAddress);
    this->groupBoxMemory->Controls->Add(this->lblSize);
    this->groupBoxMemory->Controls->Add(this->numSize);
    this->groupBoxMemory->Controls->Add(this->btnReadMemory);

    // Output GroupBox
    this->groupBoxOutput->Location = System::Drawing::Point(408, 310);
    this->groupBoxOutput->Size = System::Drawing::Size(380, 240);
    this->groupBoxOutput->Text = L"Output";
    this->groupBoxOutput->TabIndex = 4;

    this->comboBoxDataType->Location = System::Drawing::Point(15, 25);
    this->comboBoxDataType->Size = System::Drawing::Size(120, 25);
    this->comboBoxDataType->DropDownStyle = ComboBoxStyle::DropDownList;
    this->comboBoxDataType->Items->Add("Hex");
    this->comboBoxDataType->Items->Add("ASCII");
    this->comboBoxDataType->Items->Add("Unicode");
    this->comboBoxDataType->Items->Add("Int8");
    this->comboBoxDataType->Items->Add("Int16");
    this->comboBoxDataType->Items->Add("Int32");
    this->comboBoxDataType->Items->Add("Int64");
    this->comboBoxDataType->Items->Add("UInt8");
    this->comboBoxDataType->Items->Add("UInt16");
    this->comboBoxDataType->Items->Add("UInt32");
    this->comboBoxDataType->Items->Add("UInt64");
    this->comboBoxDataType->Items->Add("Float");
    this->comboBoxDataType->Items->Add("Double");
    this->comboBoxDataType->SelectedIndex = 0;

    this->btnCopyOutput->Location = System::Drawing::Point(150, 25);
    this->btnCopyOutput->Size = System::Drawing::Size(80, 25);
    this->btnCopyOutput->Text = L"Copy";
    this->btnCopyOutput->UseVisualStyleBackColor = true;
    this->btnCopyOutput->Click += gcnew System::EventHandler(this, &MemoryReaderForm::btnCopyOutput_Click);

    this->txtOutput->Location = System::Drawing::Point(15, 60);
    this->txtOutput->Size = System::Drawing::Size(350, 165);
    this->txtOutput->Multiline = true;
    this->txtOutput->ScrollBars = ScrollBars::Both;
    this->txtOutput->ReadOnly = true;
    this->txtOutput->Font = gcnew System::Drawing::Font("Consolas", 9);

    this->groupBoxOutput->Controls->Add(this->comboBoxDataType);
    this->groupBoxOutput->Controls->Add(this->btnCopyOutput);
    this->groupBoxOutput->Controls->Add(this->txtOutput);

    // Status Strip
    this->statusStrip->Location = System::Drawing::Point(0, 578);
    this->statusStrip->Size = System::Drawing::Size(800, 22);
    this->statusLabel->Text = L"Ready";
    this->statusStrip->Items->Add(this->statusLabel);

    // Add all controls to form
    this->Controls->Add(this->groupBoxDriver);
    this->Controls->Add(this->groupBoxProcess);
    this->Controls->Add(this->groupBoxModules);
    this->Controls->Add(this->groupBoxMemory);
    this->Controls->Add(this->groupBoxOutput);
    this->Controls->Add(this->statusStrip);

    this->ResumeLayout(false);
    this->PerformLayout();
}

// Event Handlers
System::Void MemoryReaderForm::btnInitDriver_Click(System::Object^ sender, System::EventArgs^ e)
{
    try
    {
        if (driver != nullptr)
        {
            delete driver;
            driver = nullptr;
        }

        SetStatus("Initializing driver...");
        driver = new eneio_lib();

        UpdateDriverStatus(true);
        btnRefreshProcesses->Enabled = true;
        SetStatus("Driver initialized successfully");
    }
    catch (const std::exception& ex)
    {
        UpdateDriverStatus(false);
        String^ errorMsg = gcnew String(ex.what());
        MessageBox::Show("Failed to initialize driver: " + errorMsg, "Error",
                        MessageBoxButtons::OK, MessageBoxIcon::Error);
        SetStatus("Driver initialization failed");
    }
}

System::Void MemoryReaderForm::btnRefreshProcesses_Click(System::Object^ sender, System::EventArgs^ e)
{
    if (driver == nullptr)
    {
        MessageBox::Show("Please initialize the driver first", "Warning",
                        MessageBoxButtons::OK, MessageBoxIcon::Warning);
        return;
    }

    RefreshProcessList();
}

System::Void MemoryReaderForm::btnRefreshModules_Click(System::Object^ sender, System::EventArgs^ e)
{
    if (driver == nullptr)
    {
        MessageBox::Show("Please initialize the driver first", "Warning",
                        MessageBoxButtons::OK, MessageBoxIcon::Warning);
        return;
    }

    RefreshModuleList();
}

System::Void MemoryReaderForm::btnReadMemory_Click(System::Object^ sender, System::EventArgs^ e)
{
    if (driver == nullptr)
    {
        MessageBox::Show("Please initialize the driver first", "Warning",
                        MessageBoxButtons::OK, MessageBoxIcon::Warning);
        return;
    }

    ReadAndDisplayMemory();
}

System::Void MemoryReaderForm::btnCopyOutput_Click(System::Object^ sender, System::EventArgs^ e)
{
    if (!String::IsNullOrEmpty(txtOutput->Text))
    {
        Clipboard::SetText(txtOutput->Text);
        SetStatus("Output copied to clipboard");
    }
}

System::Void MemoryReaderForm::comboBoxProcesses_SelectedIndexChanged(System::Object^ sender, System::EventArgs^ e)
{
    if (comboBoxProcesses->SelectedItem != nullptr)
    {
        ProcessInfo^ procInfo = safe_cast<ProcessInfo^>(comboBoxProcesses->SelectedItem);
        lblProcessInfo->Text = String::Format("PID: {0}\nBase: 0x{1:X}",
                                            procInfo->PID, procInfo->BaseAddress);

        // Enable module refresh
        btnRefreshModules->Enabled = true;
        listBoxModules->Enabled = true;

        // Clear previous modules
        listBoxModules->Items->Clear();

        SetStatus("Process selected: " + procInfo->Name);
    }
}

System::Void MemoryReaderForm::listBoxModules_SelectedIndexChanged(System::Object^ sender, System::EventArgs^ e)
{
    if (listBoxModules->SelectedItem != nullptr)
    {
        ModuleInfo^ modInfo = safe_cast<ModuleInfo^>(listBoxModules->SelectedItem);
        txtAddress->Text = String::Format("0x{0:X}", modInfo->BaseAddress);

        SetStatus("Module selected: " + modInfo->Name);
    }
}

System::Void MemoryReaderForm::txtProcessFilter_TextChanged(System::Object^ sender, System::EventArgs^ e)
{
    ApplyProcessFilter();
}

void MemoryReaderForm::UpdateDriverStatus(bool initialized)
{
    if (initialized)
    {
        lblDriverStatus->Text = L"Driver initialized successfully";
        lblDriverStatus->ForeColor = System::Drawing::Color::Green;
        btnInitDriver->Text = L"Reinitialize Driver";
    }
    else
    {
        lblDriverStatus->Text = L"Driver not initialized";
        lblDriverStatus->ForeColor = System::Drawing::Color::Red;
        btnInitDriver->Text = L"Initialize Driver";
        btnRefreshProcesses->Enabled = false;
        btnRefreshModules->Enabled = false;
        listBoxModules->Enabled = false;
        comboBoxProcesses->Items->Clear();
        listBoxModules->Items->Clear();
    }
}

void MemoryReaderForm::RefreshProcessList()
{
    SetStatus("Refreshing process list...");
    allProcesses->Clear();
    comboBoxProcesses->Items->Clear();

    try
    {
        // Get list of running processes using Windows API
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE)
            return;

        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32))
        {
            do
            {
                // Convert process name to managed string
                String^ processName = gcnew String(pe32.szExeFile);

                // Try to get process base address using our driver
                pin_ptr<const wchar_t> wch = PtrToStringChars(processName);
                std::wstring wstr(wch);
                std::string stdProcessName(wstr.begin(), wstr.end());
                uintptr_t baseAddr = driver->get_process_base(stdProcessName.c_str());

                if (baseAddr != 0)
                {
                    ProcessInfo^ procInfo = gcnew ProcessInfo(processName, pe32.th32ProcessID, baseAddr);
                    allProcesses->Add(procInfo);
                }

            } while (Process32Next(hSnapshot, &pe32));
        }

        CloseHandle(hSnapshot);

        // Apply current filter
        ApplyProcessFilter();

        SetStatus(String::Format("Found {0} accessible processes", allProcesses->Count));
    }
    catch (...)
    {
        SetStatus("Error refreshing process list");
    }
}

void MemoryReaderForm::RefreshModuleList()
{
    if (comboBoxProcesses->SelectedItem == nullptr)
    {
        MessageBox::Show("Please select a process first", "Warning",
                        MessageBoxButtons::OK, MessageBoxIcon::Warning);
        return;
    }

    ProcessInfo^ procInfo = safe_cast<ProcessInfo^>(comboBoxProcesses->SelectedItem);
    pin_ptr<const wchar_t> wch = PtrToStringChars(procInfo->Name);
    std::wstring wstr(wch);
    std::string processName(wstr.begin(), wstr.end());

    SetStatus("Refreshing module list...");
    listBoxModules->Items->Clear();

    try
    {
        SetStatus(String::Format("Getting PEB for process: {0} (PID: {1})", procInfo->Name, procInfo->PID));

        // Get PEB address
        uintptr_t pebAddr = driver->get_process_peb(processName.c_str());
        if (pebAddr == 0)
        {
            SetStatus(String::Format("Failed to get PEB address for {0}. Try using console mode for debugging.", procInfo->Name));

            // Try alternative: use the driver's built-in module enumeration for debugging
            pin_ptr<const wchar_t> wch = PtrToStringChars(procInfo->Name);
            std::wstring wstr(wch);
            std::string stdProcessName(wstr.begin(), wstr.end());

            // This will print debug info to console
            driver->enumerate_process_modules(stdProcessName.c_str());

            return;
        }

        SetStatus(String::Format("PEB address: 0x{0:X}", pebAddr));

        // Enumerate modules from PEB
        EnumerateModulesFromPEB(pebAddr, procInfo->Name);

        SetStatus(String::Format("Found {0} modules", listBoxModules->Items->Count));
    }
    catch (...)
    {
        SetStatus("Error refreshing module list");
    }
}

void MemoryReaderForm::ReadAndDisplayMemory()
{
    try
    {
        String^ addressStr = txtAddress->Text->Trim();
        if (String::IsNullOrEmpty(addressStr))
        {
            MessageBox::Show("Please enter a memory address", "Warning",
                            MessageBoxButtons::OK, MessageBoxIcon::Warning);
            return;
        }

        // Parse address (support both 0x prefix and plain hex)
        uintptr_t address;
        if (addressStr->StartsWith("0x") || addressStr->StartsWith("0X"))
        {
            addressStr = addressStr->Substring(2);
        }

        pin_ptr<const wchar_t> wch = PtrToStringChars(addressStr);
        std::wstring wstr(wch);
        std::string stdAddressStr(wstr.begin(), wstr.end());

        address = std::stoull(stdAddressStr, nullptr, 16);

        int size = (int)numSize->Value;

        // Read memory
        std::vector<uint8_t> buffer(size);
        bool success = driver->read_virtual_memory(address, buffer.data(), size);

        if (!success)
        {
            txtOutput->Text = "Failed to read memory at address 0x" + address.ToString("X");
            SetStatus("Memory read failed");
            return;
        }

        // Convert to managed array
        array<System::Byte>^ managedBuffer = gcnew array<System::Byte>(size);
        for (int i = 0; i < size; i++)
        {
            managedBuffer[i] = buffer[i];
        }

        // Format output based on selected data type
        String^ dataType = comboBoxDataType->SelectedItem->ToString();
        String^ output = FormatDataAsType(managedBuffer, dataType);

        txtOutput->Text = output;
        SetStatus(String::Format("Successfully read {0} bytes from 0x{1:X}", size, address));
    }
    catch (const std::exception& ex)
    {
        String^ errorMsg = gcnew String(ex.what());
        txtOutput->Text = "Error: " + errorMsg;
        SetStatus("Memory read error");
    }
    catch (...)
    {
        txtOutput->Text = "Unknown error occurred while reading memory";
        SetStatus("Memory read error");
    }
}

String^ MemoryReaderForm::FormatDataAsType(array<System::Byte>^ data, String^ dataType)
{
    if (data->Length == 0)
        return "No data";

    System::Text::StringBuilder^ sb = gcnew System::Text::StringBuilder();

    if (dataType == "Hex")
    {
        return ByteArrayToHexString(data);
    }
    else if (dataType == "ASCII")
    {
        for (int i = 0; i < data->Length; i++)
        {
            char c = (char)data[i];
            if (c >= 32 && c <= 126)
                sb->Append(c);
            else
                sb->Append('.');
        }
        return sb->ToString();
    }
    else if (dataType == "Unicode")
    {
        if (data->Length >= 2)
        {
            for (int i = 0; i < data->Length - 1; i += 2)
            {
                wchar_t wc = (wchar_t)(data[i] | (data[i + 1] << 8));
                if (wc >= 32 && wc <= 126)
                    sb->Append((char)wc);
                else
                    sb->Append('.');
            }
        }
        return sb->ToString();
    }
    else if (dataType == "Int8" && data->Length >= 1)
    {
        return ((System::SByte)data[0]).ToString();
    }
    else if (dataType == "Int16" && data->Length >= 2)
    {
        short value = (short)(data[0] | (data[1] << 8));
        return value.ToString();
    }
    else if (dataType == "Int32" && data->Length >= 4)
    {
        int value = data[0] | (data[1] << 8) | (data[2] << 16) | (data[3] << 24);
        return value.ToString();
    }
    else if (dataType == "Int64" && data->Length >= 8)
    {
        long long value = 0;
        for (int i = 0; i < 8; i++)
        {
            value |= ((long long)data[i]) << (i * 8);
        }
        return value.ToString();
    }
    else if (dataType == "UInt8" && data->Length >= 1)
    {
        return data[0].ToString();
    }
    else if (dataType == "UInt16" && data->Length >= 2)
    {
        unsigned short value = (unsigned short)(data[0] | (data[1] << 8));
        return value.ToString();
    }
    else if (dataType == "UInt32" && data->Length >= 4)
    {
        unsigned int value = data[0] | (data[1] << 8) | (data[2] << 16) | (data[3] << 24);
        return value.ToString();
    }
    else if (dataType == "UInt64" && data->Length >= 8)
    {
        unsigned long long value = 0;
        for (int i = 0; i < 8; i++)
        {
            value |= ((unsigned long long)data[i]) << (i * 8);
        }
        return value.ToString();
    }
    else if (dataType == "Float" && data->Length >= 4)
    {
        pin_ptr<System::Byte> pinnedData = &data[0];
        float value = *((float*)pinnedData);
        return value.ToString("F6");
    }
    else if (dataType == "Double" && data->Length >= 8)
    {
        pin_ptr<System::Byte> pinnedData = &data[0];
        double value = *((double*)pinnedData);
        return value.ToString("F6");
    }

    return "Insufficient data for selected type";
}

String^ MemoryReaderForm::ByteArrayToHexString(array<System::Byte>^ data)
{
    System::Text::StringBuilder^ sb = gcnew System::Text::StringBuilder();

    for (int i = 0; i < data->Length; i++)
    {
        if (i > 0 && i % 16 == 0)
            sb->AppendLine();
        else if (i > 0 && i % 8 == 0)
            sb->Append("  ");
        else if (i > 0)
            sb->Append(" ");

        sb->AppendFormat("{0:X2}", data[i]);
    }

    return sb->ToString();
}

void MemoryReaderForm::SetStatus(String^ message)
{
    statusLabel->Text = message;
    statusStrip->Refresh();
}

void MemoryReaderForm::EnumerateModulesFromPEB(uintptr_t pebAddr, String^ processName)
{
    try
    {
        SetStatus("Reading PEB structure...");

        // PEB + 0x18 = PEB_LDR_DATA
        uintptr_t ldrDataPtr = 0;
        if (!driver->read_virtual_memory(pebAddr + 0x18, &ldrDataPtr, sizeof(ldrDataPtr)))
        {
            SetStatus("Failed to read LDR data pointer");
            return;
        }

        if (ldrDataPtr == 0)
        {
            SetStatus("LDR data pointer is null");
            return;
        }

        // PEB_LDR_DATA + 0x20 = InMemoryOrderModuleList
        uintptr_t moduleList = 0;
        if (!driver->read_virtual_memory(ldrDataPtr + 0x20, &moduleList, sizeof(moduleList)))
        {
            SetStatus("Failed to read module list");
            return;
        }

        if (moduleList == 0)
        {
            SetStatus("Module list is null");
            return;
        }

        uintptr_t currentEntry = moduleList;
        int moduleCount = 0;
        const int maxModules = 10; // Limit to first 10 modules like the original code

        for (int i = 0; i < maxModules && currentEntry != 0; i++)
        {
            // Validate current entry address
            if (currentEntry < 0x1000)
            {
                SetStatus(String::Format("Invalid module entry address: 0x{0:X}", currentEntry));
                break;
            }

            // From list entry, subtract offset to get actual LDR_DATA_TABLE_ENTRY
            uintptr_t ldrEntry = currentEntry - 0x10;

            // DllBase at offset 0x30
            uintptr_t dllBase = 0;
            if (!driver->read_virtual_memory(ldrEntry + 0x30, &dllBase, sizeof(dllBase)))
            {
                SetStatus(String::Format("Failed to read DllBase at entry {0}", i));
                break;
            }

            // SizeOfImage at offset 0x40
            uintptr_t sizeOfImage = 0;
            if (!driver->read_virtual_memory(ldrEntry + 0x40, &sizeOfImage, sizeof(sizeOfImage)))
            {
                SetStatus(String::Format("Failed to read SizeOfImage at entry {0}", i));
                break;
            }

            // BaseDllName at offset 0x58 (UNICODE_STRING)
            UNICODE_STRING_NATIVE unicodeString;

            std::vector<wchar_t> moduleName(256, 0);
            if (driver->read_virtual_memory(ldrEntry + 0x58, &unicodeString, sizeof(unicodeString)))
            {
                if (unicodeString.Buffer && unicodeString.Length > 0 && unicodeString.Length < 512)
                {
                    size_t readSize = min(unicodeString.Length, (uint16_t)(moduleName.size() * sizeof(wchar_t) - 2));
                    if (driver->read_virtual_memory(unicodeString.Buffer, moduleName.data(), readSize))
                    {
                        String^ moduleNameStr = gcnew String(moduleName.data());
                        if (!String::IsNullOrEmpty(moduleNameStr))
                        {
                            ModuleInfo^ modInfo = gcnew ModuleInfo(moduleNameStr, dllBase, (uint32_t)sizeOfImage);
                            listBoxModules->Items->Add(modInfo);
                            moduleCount++;
                        }
                    }
                }
            }

            // Get next entry
            uintptr_t nextEntry = 0;
            if (!driver->read_virtual_memory(currentEntry, &nextEntry, sizeof(nextEntry)))
            {
                SetStatus(String::Format("Failed to read next entry at module {0}", i));
                break;
            }

            currentEntry = nextEntry;
        }

        if (moduleCount == 0)
        {
            SetStatus("No modules found");
        }
        else
        {
            SetStatus(String::Format("Successfully enumerated {0} modules", moduleCount));
        }
    }
    catch (const std::exception& ex)
    {
        String^ errorMsg = gcnew String(ex.what());
        SetStatus("Exception during module enumeration: " + errorMsg);
    }
    catch (...)
    {
        SetStatus("Unknown error during module enumeration");
    }
}

void MemoryReaderForm::ApplyProcessFilter()
{
    String^ filterText = txtProcessFilter->Text->Trim()->ToLower();
    comboBoxProcesses->Items->Clear();

    for each (ProcessInfo^ procInfo in allProcesses)
    {
        if (String::IsNullOrEmpty(filterText) ||
            procInfo->Name->ToLower()->Contains(filterText))
        {
            comboBoxProcesses->Items->Add(procInfo);
        }
    }
}