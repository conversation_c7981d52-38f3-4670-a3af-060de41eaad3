[*] Cleaning up any existing services...
[+] Driver initialized successfully
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DeltaForceClie', input='[System Process]', PID=14040
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DeltaForceC<PERSON>', input='System', PID=14040
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DeltaForceClie', input='Registry', PID=14040
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DeltaForceClie', input='smss.exe', PID=14040
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='csrss.exe', input='csrss.exe', PID=612
[DEBUG] Checking process: kernel='DeltaForce<PERSON><PERSON>', input='csrss.exe', PID=14040
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DeltaForceClie', input='wininit.exe', PID=14040
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='csrss.exe', input='csrss.exe', PID=612
[DEBUG] Checking process: kernel='DeltaForceClie', input='csrss.exe', PID=14040
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='services.exe', input='services.exe', PID=864
[FOUND] Matching process:
  Kernel name: services.exe
  Full name: services.exe
  PID: 864
  Base: 0x7ff791d20000
  CR3: 0x217e000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='lsass.exe', input='lsass.exe', PID=876
[FOUND] Matching process:
  Kernel name: lsass.exe
  Full name: lsass.exe
  PID: 876
  Base: 0x7ff721a90000
  CR3: 0x862da6000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='svchost.exe', input='svchost.exe', PID=772
[FOUND] Matching process:
  Kernel name: svchost.exe
  Full name: svchost.exe
  PID: 772
  Base: 0x7ff7be6e0000
  CR3: 0x85f957000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='fontdrvhost.ex', input='fontdrvhost.exe', PID=900
[FOUND] Matching process:
  Kernel name: fontdrvhost.ex
  Full name: fontdrvhost.exe
  PID: 900
  Base: 0x7ff62b2d0000
  CR3: 0x8617e0000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='winlogon.exe', input='winlogon.exe', PID=1104
[FOUND] Matching process:
  Kernel name: winlogon.exe
  Full name: winlogon.exe
  PID: 1104
  Base: 0x7ff6f0510000
  CR3: 0x860b04000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='fontdrvhost.ex', input='fontdrvhost.exe', PID=900
[FOUND] Matching process:
  Kernel name: fontdrvhost.ex
  Full name: fontdrvhost.exe
  PID: 900
  Base: 0x7ff62b2d0000
  CR3: 0x8617e0000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='svchost.exe', input='svchost.exe', PID=772
[FOUND] Matching process:
  Kernel name: svchost.exe
  Full name: svchost.exe
  PID: 772
  Base: 0x7ff7be6e0000
  CR3: 0x85f957000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='svchost.exe', input='svchost.exe', PID=772
[FOUND] Matching process:
  Kernel name: svchost.exe
  Full name: svchost.exe
  PID: 772
  Base: 0x7ff7be6e0000
  CR3: 0x85f957000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='svchost.exe', input='svchost.exe', PID=772
[FOUND] Matching process:
  Kernel name: svchost.exe
  Full name: svchost.exe
  PID: 772
  Base: 0x7ff7be6e0000
  CR3: 0x85f957000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='svchost.exe', input='svchost.exe', PID=772
[FOUND] Matching process:
  Kernel name: svchost.exe
  Full name: svchost.exe
  PID: 772
  Base: 0x7ff7be6e0000
  CR3: 0x85f957000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='dwm.exe', input='dwm.exe', PID=1432
[FOUND] Matching process:
  Kernel name: dwm.exe
  Full name: dwm.exe
  PID: 1432
  Base: 0x7ff6cc4a0000
  CR3: 0x869d5d000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='svchost.exe', input='svchost.exe', PID=772
[FOUND] Matching process:
  Kernel name: svchost.exe
  Full name: svchost.exe
  PID: 772
  Base: 0x7ff7be6e0000
  CR3: 0x85f957000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='svchost.exe', input='svchost.exe', PID=772
[FOUND] Matching process:
  Kernel name: svchost.exe
  Full name: svchost.exe
  PID: 772
  Base: 0x7ff7be6e0000
  CR3: 0x85f957000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='svchost.exe', input='svchost.exe', PID=772
[FOUND] Matching process:
  Kernel name: svchost.exe
  Full name: svchost.exe
  PID: 772
  Base: 0x7ff7be6e0000
  CR3: 0x85f957000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='svchost.exe', input='svchost.exe', PID=772
[FOUND] Matching process:
  Kernel name: svchost.exe
  Full name: svchost.exe
  PID: 772
  Base: 0x7ff7be6e0000
  CR3: 0x85f957000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='svchost.exe', input='svchost.exe', PID=772
[FOUND] Matching process:
  Kernel name: svchost.exe
  Full name: svchost.exe
  PID: 772
  Base: 0x7ff7be6e0000
  CR3: 0x85f957000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='svchost.exe', input='svchost.exe', PID=772
[FOUND] Matching process:
  Kernel name: svchost.exe
  Full name: svchost.exe
  PID: 772
  Base: 0x7ff7be6e0000
  CR3: 0x85f957000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='svchost.exe', input='svchost.exe', PID=772
[FOUND] Matching process:
  Kernel name: svchost.exe
  Full name: svchost.exe
  PID: 772
  Base: 0x7ff7be6e0000
  CR3: 0x85f957000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='svchost.exe', input='svchost.exe', PID=772
[FOUND] Matching process:
  Kernel name: svchost.exe
  Full name: svchost.exe
  PID: 772
  Base: 0x7ff7be6e0000
  CR3: 0x85f957000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='svchost.exe', input='svchost.exe', PID=772
[FOUND] Matching process:
  Kernel name: svchost.exe
  Full name: svchost.exe
  PID: 772
  Base: 0x7ff7be6e0000
  CR3: 0x85f957000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='nxpauxsvc.exe', input='nxpauxsvc.exe', PID=2328
[FOUND] Matching process:
  Kernel name: nxpauxsvc.exe
  Full name: nxpauxsvc.exe
  PID: 2328
  Base: 0x400000
  CR3: 0x86e955000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='nssm.exe', input='nssm.exe', PID=2336
[FOUND] Matching process:
  Kernel name: nssm.exe
  Full name: nssm.exe
  PID: 2336
  Base: 0x140000000
  CR3: 0x86eade000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='wetype_service', input='wetype_service.exe', PID=2360
[FOUND] Matching process:
  Kernel name: wetype_service
  Full name: wetype_service.exe
  PID: 2360
  Base: 0x7ff7145e0000
  CR3: 0x86e9c2000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='conhost.exe', input='conhost.exe', PID=2464
[FOUND] Matching process:
  Kernel name: conhost.exe
  Full name: conhost.exe
  PID: 2464
  Base: 0x7ff7e13f0000
  CR3: 0x84698d000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='windows_export', input='windows_exporter.exe', PID=2552
[FOUND] Matching process:
  Kernel name: windows_export
  Full name: windows_exporter.exe
  PID: 2552
  Base: 0xc0000
  CR3: 0x871936000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='WmiPrvSE.exe', input='WmiPrvSE.exe', PID=2912
[FOUND] Matching process:
  Kernel name: WmiPrvSE.exe
  Full name: WmiPrvSE.exe
  PID: 2912
  Base: 0x7ff7da390000
  CR3: 0x87933f000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='svchost.exe', input='svchost.exe', PID=772
[FOUND] Matching process:
  Kernel name: svchost.exe
  Full name: svchost.exe
  PID: 772
  Base: 0x7ff7be6e0000
  CR3: 0x85f957000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='sihost.exe', input='sihost.exe', PID=3016
[FOUND] Matching process:
  Kernel name: sihost.exe
  Full name: sihost.exe
  PID: 3016
  Base: 0x7ff60d1b0000
  CR3: 0x87d4fa000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='svchost.exe', input='svchost.exe', PID=772
[FOUND] Matching process:
  Kernel name: svchost.exe
  Full name: svchost.exe
  PID: 772
  Base: 0x7ff7be6e0000
  CR3: 0x85f957000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='ctfmon.exe', input='ctfmon.exe', PID=3228
[FOUND] Matching process:
  Kernel name: ctfmon.exe
  Full name: ctfmon.exe
  PID: 3228
  Base: 0x7ff617630000
  CR3: 0x181910000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='explorer.exe', input='explorer.exe', PID=3368
[FOUND] Matching process:
  Kernel name: explorer.exe
  Full name: explorer.exe
  PID: 3368
  Base: 0x7ff7e28c0000
  CR3: 0x1842c5000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='ChsIME.exe', input='ChsIME.exe', PID=3716
[FOUND] Matching process:
  Kernel name: ChsIME.exe
  Full name: ChsIME.exe
  PID: 3716
  Base: 0x7ff712480000
  CR3: 0x189b14000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='dllhost.exe', input='dllhost.exe', PID=3912
[FOUND] Matching process:
  Kernel name: dllhost.exe
  Full name: dllhost.exe
  PID: 3912
  Base: 0x7ff7a8be0000
  CR3: 0x191fb9000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='StartMenuExper', input='StartMenuExperienceHost.exe', PID=3984
[FOUND] Matching process:
  Kernel name: StartMenuExper
  Full name: StartMenuExperienceHost.exe
  PID: 3984
  Base: 0x7ff667030000
  CR3: 0x1949ab000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='RuntimeBroker.', input='RuntimeBroker.exe', PID=4064
[FOUND] Matching process:
  Kernel name: RuntimeBroker.
  Full name: RuntimeBroker.exe
  PID: 4064
  Base: 0x7ff7f28d0000
  CR3: 0x19186d000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='SearchApp.exe', input='SearchApp.exe', PID=3204
[FOUND] Matching process:
  Kernel name: SearchApp.exe
  Full name: SearchApp.exe
  PID: 3204
  Base: 0x7ff711d60000
  CR3: 0x1950fd000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='RuntimeBroker.', input='RuntimeBroker.exe', PID=4064
[FOUND] Matching process:
  Kernel name: RuntimeBroker.
  Full name: RuntimeBroker.exe
  PID: 4064
  Base: 0x7ff7f28d0000
  CR3: 0x19186d000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='wetype_update.', input='wetype_update.exe', PID=4536
[FOUND] Matching process:
  Kernel name: wetype_update.
  Full name: wetype_update.exe
  PID: 4536
  Base: 0x7ff79c080000
  CR3: 0x87c181000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='wetype_rendere', input='wetype_renderer.exe', PID=4556
[FOUND] Matching process:
  Kernel name: wetype_rendere
  Full name: wetype_renderer.exe
  PID: 4556
  Base: 0x7ff62ef30000
  CR3: 0x87a827000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='wetype_server.', input='wetype_server.exe', PID=4568
[FOUND] Matching process:
  Kernel name: wetype_server.
  Full name: wetype_server.exe
  PID: 4568
  Base: 0x7ff6ef230000
  CR3: 0x192e8e000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='conhost.exe', input='conhost.exe', PID=2464
[FOUND] Matching process:
  Kernel name: conhost.exe
  Full name: conhost.exe
  PID: 2464
  Base: 0x7ff7e13f0000
  CR3: 0x84698d000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='TextInputHost.', input='TextInputHost.exe', PID=4660
[FOUND] Matching process:
  Kernel name: TextInputHost.
  Full name: TextInputHost.exe
  PID: 4660
  Base: 0x7ff78cb30000
  CR3: 0x199d5c000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='nxprun.exe', input='nxprun.exe', PID=4760
[FOUND] Matching process:
  Kernel name: nxprun.exe
  Full name: nxprun.exe
  PID: 4760
  Base: 0x400000
  CR3: 0x19c48b000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='knbmenu.exe', input='knbmenu.exe', PID=3384
[FOUND] Matching process:
  Kernel name: knbmenu.exe
  Full name: knbmenu.exe
  PID: 3384
  Base: 0x400000
  CR3: 0x1ac7d1000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='vgtray.exe', input='vgtray.exe', PID=5196
[FOUND] Matching process:
  Kernel name: vgtray.exe
  Full name: vgtray.exe
  PID: 5196
  Base: 0x7ff7c65e0000
  CR3: 0x1ab726000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='ACE-Tray.exe', input='ACE-Tray.exe', PID=5864
[FOUND] Matching process:
  Kernel name: ACE-Tray.exe
  Full name: ACE-Tray.exe
  PID: 5864
  Base: 0x7ff721f00000
  CR3: 0x1b4152000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='NVDisplay.Cont', input='NVDisplay.Container.exe', PID=6024
[FOUND] Matching process:
  Kernel name: NVDisplay.Cont
  Full name: NVDisplay.Container.exe
  PID: 6024
  Base: 0x7ff7463f0000
  CR3: 0x1b79f7000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='msedgewebview2', input='msedgewebview2.exe', PID=6112
[FOUND] Matching process:
  Kernel name: msedgewebview2
  Full name: msedgewebview2.exe
  PID: 6112
  Base: 0x7ff72cd70000
  CR3: 0x1bf02d000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='NVDisplay.Cont', input='NVDisplay.Container.exe', PID=6024
[FOUND] Matching process:
  Kernel name: NVDisplay.Cont
  Full name: NVDisplay.Container.exe
  PID: 6024
  Base: 0x7ff7463f0000
  CR3: 0x1b79f7000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='msedgewebview2', input='msedgewebview2.exe', PID=6112
[FOUND] Matching process:
  Kernel name: msedgewebview2
  Full name: msedgewebview2.exe
  PID: 6112
  Base: 0x7ff72cd70000
  CR3: 0x1bf02d000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='WmiPrvSE.exe', input='WmiPrvSE.exe', PID=2912
[FOUND] Matching process:
  Kernel name: WmiPrvSE.exe
  Full name: WmiPrvSE.exe
  PID: 2912
  Base: 0x7ff7da390000
  CR3: 0x87933f000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='msedgewebview2', input='msedgewebview2.exe', PID=6112
[FOUND] Matching process:
  Kernel name: msedgewebview2
  Full name: msedgewebview2.exe
  PID: 6112
  Base: 0x7ff72cd70000
  CR3: 0x1bf02d000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='msedgewebview2', input='msedgewebview2.exe', PID=6112
[FOUND] Matching process:
  Kernel name: msedgewebview2
  Full name: msedgewebview2.exe
  PID: 6112
  Base: 0x7ff72cd70000
  CR3: 0x1bf02d000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='msedgewebview2', input='msedgewebview2.exe', PID=6112
[FOUND] Matching process:
  Kernel name: msedgewebview2
  Full name: msedgewebview2.exe
  PID: 6112
  Base: 0x7ff72cd70000
  CR3: 0x1bf02d000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='msedgewebview2', input='msedgewebview2.exe', PID=6112
[FOUND] Matching process:
  Kernel name: msedgewebview2
  Full name: msedgewebview2.exe
  PID: 6112
  Base: 0x7ff72cd70000
  CR3: 0x1bf02d000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='msedgewebview2', input='msedgewebview2.exe', PID=6112
[FOUND] Matching process:
  Kernel name: msedgewebview2
  Full name: msedgewebview2.exe
  PID: 6112
  Base: 0x7ff72cd70000
  CR3: 0x1bf02d000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='RuntimeBroker.', input='RuntimeBroker.exe', PID=4064
[FOUND] Matching process:
  Kernel name: RuntimeBroker.
  Full name: RuntimeBroker.exe
  PID: 4064
  Base: 0x7ff7f28d0000
  CR3: 0x19186d000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='WUDFHost.exe', input='WUDFHost.exe', PID=5696
[FOUND] Matching process:
  Kernel name: WUDFHost.exe
  Full name: WUDFHost.exe
  PID: 5696
  Base: 0x7ff69aa70000
  CR3: 0x7f0970000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='WUDFHost.exe', input='WUDFHost.exe', PID=5696
[FOUND] Matching process:
  Kernel name: WUDFHost.exe
  Full name: WUDFHost.exe
  PID: 5696
  Base: 0x7ff69aa70000
  CR3: 0x7f0970000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DaasAgent.exe', input='DaasAgent.exe', PID=8036
[FOUND] Matching process:
  Kernel name: DaasAgent.exe
  Full name: DaasAgent.exe
  PID: 8036
  Base: 0x7ff732a50000
  CR3: 0x189377000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DaasAgent.exe', input='DaasAgent.exe', PID=8036
[FOUND] Matching process:
  Kernel name: DaasAgent.exe
  Full name: DaasAgent.exe
  PID: 8036
  Base: 0x7ff732a50000
  CR3: 0x189377000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DaasAgent.exe', input='DaasAgent.exe', PID=8036
[FOUND] Matching process:
  Kernel name: DaasAgent.exe
  Full name: DaasAgent.exe
  PID: 8036
  Base: 0x7ff732a50000
  CR3: 0x189377000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='TabTip.exe', input='TabTip.exe', PID=7376
[FOUND] Matching process:
  Kernel name: TabTip.exe
  Full name: TabTip.exe
  PID: 7376
  Base: 0x7ff7dac00000
  CR3: 0x7ef8f8000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='audiodg.exe', input='audiodg.exe', PID=1428
[FOUND] Matching process:
  Kernel name: audiodg.exe
  Full name: audiodg.exe
  PID: 1428
  Base: 0x7ff7eb740000
  CR3: 0x8050dc000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='categraf.exe', input='categraf.exe', PID=4260
[FOUND] Matching process:
  Kernel name: categraf.exe
  Full name: categraf.exe
  PID: 4260
  Base: 0x400000
  CR3: 0x821aac000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='WmiPrvSE.exe', input='WmiPrvSE.exe', PID=2912
[FOUND] Matching process:
  Kernel name: WmiPrvSE.exe
  Full name: WmiPrvSE.exe
  PID: 2912
  Base: 0x7ff7da390000
  CR3: 0x87933f000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='Taskmgr.exe', input='Taskmgr.exe', PID=7492
[FOUND] Matching process:
  Kernel name: Taskmgr.exe
  Full name: Taskmgr.exe
  PID: 7492
  Base: 0x7ff7b7660000
  CR3: 0x8283e5000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='wegame.exe', input='wegame.exe', PID=7736
[FOUND] Matching process:
  Kernel name: wegame.exe
  Full name: wegame.exe
  PID: 7736
  Base: 0x400000
  CR3: 0x828db9000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='tcls_core.exe', input='tcls_core.exe', PID=7996
[FOUND] Matching process:
  Kernel name: tcls_core.exe
  Full name: tcls_core.exe
  PID: 7996
  Base: 0x400000
  CR3: 0x14d330000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='UserOOBEBroker', input='UserOOBEBroker.exe', PID=8784
[FOUND] Matching process:
  Kernel name: UserOOBEBroker
  Full name: UserOOBEBroker.exe
  PID: 8784
  Base: 0x7ff6fb2b0000
  CR3: 0x16882d000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='rail.exe', input='rail.exe', PID=8964
[FOUND] Matching process:
  Kernel name: rail.exe
  Full name: rail.exe
  PID: 8964
  Base: 0x400000
  CR3: 0x173e85000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='csrss.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=612
[DEBUG] Checking process: kernel='services.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=864
[DEBUG] Checking process: kernel='lsass.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=876
[DEBUG] Checking process: kernel='svchost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=772
[DEBUG] Checking process: kernel='fontdrvhost.ex', input='DeltaForceClient-Win64-Shipping.exe', PID=900
[DEBUG] Checking process: kernel='winlogon.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=1104
[DEBUG] Checking process: kernel='fontdrvhost.ex', input='DeltaForceClient-Win64-Shipping.exe', PID=1160
[DEBUG] Checking process: kernel='svchost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=1196
[DEBUG] Checking process: kernel='svchost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=1300
[DEBUG] Checking process: kernel='svchost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=1328
[DEBUG] Checking process: kernel='svchost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=1384
[DEBUG] Checking process: kernel='dwm.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=1432
[DEBUG] Checking process: kernel='svchost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=1540
[DEBUG] Checking process: kernel='svchost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=1672
[DEBUG] Checking process: kernel='svchost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=1884
[DEBUG] Checking process: kernel='svchost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=892
[DEBUG] Checking process: kernel='svchost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=1908
[DEBUG] Checking process: kernel='svchost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=1964
[DEBUG] Checking process: kernel='svchost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=2080
[DEBUG] Checking process: kernel='svchost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=2128
[DEBUG] Checking process: kernel='svchost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=2256
[DEBUG] Checking process: kernel='nxpauxsvc.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=2328
[DEBUG] Checking process: kernel='nssm.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=2336
[DEBUG] Checking process: kernel='wetype_service', input='DeltaForceClient-Win64-Shipping.exe', PID=2360
[DEBUG] Checking process: kernel='conhost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=2464
[DEBUG] Checking process: kernel='windows_export', input='DeltaForceClient-Win64-Shipping.exe', PID=2552
[DEBUG] Checking process: kernel='WmiPrvSE.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=2912
[DEBUG] Checking process: kernel='svchost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=824
[DEBUG] Checking process: kernel='sihost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=3016
[DEBUG] Checking process: kernel='svchost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=3056
[DEBUG] Checking process: kernel='ctfmon.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=3228
[DEBUG] Checking process: kernel='explorer.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=3368
[DEBUG] Checking process: kernel='ChsIME.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=3716
[DEBUG] Checking process: kernel='dllhost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=3912
[DEBUG] Checking process: kernel='StartMenuExper', input='DeltaForceClient-Win64-Shipping.exe', PID=3984
[DEBUG] Checking process: kernel='RuntimeBroker.', input='DeltaForceClient-Win64-Shipping.exe', PID=4064
[DEBUG] Checking process: kernel='SearchApp.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=3204
[DEBUG] Checking process: kernel='RuntimeBroker.', input='DeltaForceClient-Win64-Shipping.exe', PID=4108
[DEBUG] Checking process: kernel='wetype_update.', input='DeltaForceClient-Win64-Shipping.exe', PID=4536
[DEBUG] Checking process: kernel='wetype_rendere', input='DeltaForceClient-Win64-Shipping.exe', PID=4556
[DEBUG] Checking process: kernel='wetype_server.', input='DeltaForceClient-Win64-Shipping.exe', PID=4568
[DEBUG] Checking process: kernel='conhost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=4588
[DEBUG] Checking process: kernel='TextInputHost.', input='DeltaForceClient-Win64-Shipping.exe', PID=4660
[DEBUG] Checking process: kernel='nxprun.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=4760
[DEBUG] Checking process: kernel='knbmenu.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=3384
[DEBUG] Checking process: kernel='vgtray.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=5196
[DEBUG] Checking process: kernel='ACE-Tray.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=5864
[DEBUG] Checking process: kernel='NVDisplay.Cont', input='DeltaForceClient-Win64-Shipping.exe', PID=6024
[DEBUG] Checking process: kernel='msedgewebview2', input='DeltaForceClient-Win64-Shipping.exe', PID=6112
[DEBUG] Checking process: kernel='NVDisplay.Cont', input='DeltaForceClient-Win64-Shipping.exe', PID=5964
[DEBUG] Checking process: kernel='msedgewebview2', input='DeltaForceClient-Win64-Shipping.exe', PID=6156
[DEBUG] Checking process: kernel='WmiPrvSE.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=6388
[DEBUG] Checking process: kernel='msedgewebview2', input='DeltaForceClient-Win64-Shipping.exe', PID=6432
[DEBUG] Checking process: kernel='msedgewebview2', input='DeltaForceClient-Win64-Shipping.exe', PID=6868
[DEBUG] Checking process: kernel='msedgewebview2', input='DeltaForceClient-Win64-Shipping.exe', PID=6884
[DEBUG] Checking process: kernel='msedgewebview2', input='DeltaForceClient-Win64-Shipping.exe', PID=6924
[DEBUG] Checking process: kernel='msedgewebview2', input='DeltaForceClient-Win64-Shipping.exe', PID=7116
[DEBUG] Checking process: kernel='RuntimeBroker.', input='DeltaForceClient-Win64-Shipping.exe', PID=7612
[DEBUG] Checking process: kernel='WUDFHost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=5696
[DEBUG] Checking process: kernel='WUDFHost.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=7488
[DEBUG] Checking process: kernel='DaasAgent.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=8036
[DEBUG] Checking process: kernel='DaasAgent.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=4924
[DEBUG] Checking process: kernel='DaasAgent.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=4796
[DEBUG] Checking process: kernel='TabTip.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=7376
[DEBUG] Checking process: kernel='audiodg.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=1428
[DEBUG] Checking process: kernel='categraf.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=4260
[DEBUG] Checking process: kernel='WmiPrvSE.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=7696
[DEBUG] Checking process: kernel='Taskmgr.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=7492
[DEBUG] Checking process: kernel='wegame.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=7736
[DEBUG] Checking process: kernel='tcls_core.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=7996
[DEBUG] Checking process: kernel='UserOOBEBroker', input='DeltaForceClient-Win64-Shipping.exe', PID=8784
[DEBUG] Checking process: kernel='rail.exe', input='DeltaForceClient-Win64-Shipping.exe', PID=8964
[DEBUG] Checking process: kernel='DeltaForceClie', input='DeltaForceClient-Win64-Shipping.exe', PID=14040
[FOUND] Matching process:
  Kernel name: DeltaForceClie
  Full name: DeltaForceClient-Win64-Shipping.exe
  PID: 14040
  Base: 0x140000000
  CR3: 0x24bf2b000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DeltaForceClie', input='browser.exe', PID=14040
[DEBUG] Checking process: kernel='browser.exe', input='browser.exe', PID=8324
[FOUND] Matching process:
  Kernel name: browser.exe
  Full name: browser.exe
  PID: 8324
  Base: 0x7ff6bf1d0000
  CR3: 0x1c33bc000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DeltaForceClie', input='browser.exe', PID=14040
[DEBUG] Checking process: kernel='browser.exe', input='browser.exe', PID=8324
[FOUND] Matching process:
  Kernel name: browser.exe
  Full name: browser.exe
  PID: 8324
  Base: 0x7ff6bf1d0000
  CR3: 0x1c33bc000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DeltaForceClie', input='browser.exe', PID=14040
[DEBUG] Checking process: kernel='browser.exe', input='browser.exe', PID=8324
[FOUND] Matching process:
  Kernel name: browser.exe
  Full name: browser.exe
  PID: 8324
  Base: 0x7ff6bf1d0000
  CR3: 0x1c33bc000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DeltaForceClie', input='browser.exe', PID=14040
[DEBUG] Checking process: kernel='browser.exe', input='browser.exe', PID=8324
[FOUND] Matching process:
  Kernel name: browser.exe
  Full name: browser.exe
  PID: 8324
  Base: 0x7ff6bf1d0000
  CR3: 0x1c33bc000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DeltaForceClie', input='browser.exe', PID=14040
[DEBUG] Checking process: kernel='browser.exe', input='browser.exe', PID=8324
[FOUND] Matching process:
  Kernel name: browser.exe
  Full name: browser.exe
  PID: 8324
  Base: 0x7ff6bf1d0000
  CR3: 0x1c33bc000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DeltaForceClie', input='browser.exe', PID=14040
[DEBUG] Checking process: kernel='browser.exe', input='browser.exe', PID=8324
[FOUND] Matching process:
  Kernel name: browser.exe
  Full name: browser.exe
  PID: 8324
  Base: 0x7ff6bf1d0000
  CR3: 0x1c33bc000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DeltaForceClie', input='SGuardSvc64.exe', PID=14040
[DEBUG] Checking process: kernel='SGuardSvc64.ex', input='SGuardSvc64.exe', PID=9876
[FOUND] Matching process:
  Kernel name: SGuardSvc64.ex
  Full name: SGuardSvc64.exe
  PID: 9876
  Base: 0x7ff7e9280000
  CR3: 0x124b2f000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DeltaForceClie', input='SGuard64.exe', PID=14040
[DEBUG] Checking process: kernel='SGuard64.exe', input='SGuard64.exe', PID=3176
[FOUND] Matching process:
  Kernel name: SGuard64.exe
  Full name: SGuard64.exe
  PID: 3176
  Base: 0x7ff670400000
  CR3: 0x1ceb67000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DeltaForceClie', input='UnrealCEFSubProcess.exe', PID=14040
[DEBUG] Checking process: kernel='UnrealCEFSubPr', input='UnrealCEFSubProcess.exe', PID=2236
[FOUND] Matching process:
  Kernel name: UnrealCEFSubPr
  Full name: UnrealCEFSubProcess.exe
  PID: 2236
  Base: 0x7ff7b9ad0000
  CR3: 0x39a426000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DeltaForceClie', input='UnrealCEFSubProcess.exe', PID=14040
[DEBUG] Checking process: kernel='UnrealCEFSubPr', input='UnrealCEFSubProcess.exe', PID=2236
[FOUND] Matching process:
  Kernel name: UnrealCEFSubPr
  Full name: UnrealCEFSubProcess.exe
  PID: 2236
  Base: 0x7ff7b9ad0000
  CR3: 0x39a426000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='svchost.exe', input='svchost.exe', PID=772
[FOUND] Matching process:
  Kernel name: svchost.exe
  Full name: svchost.exe
  PID: 772
  Base: 0x7ff7be6e0000
  CR3: 0x85f957000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='svchost.exe', input='svchost.exe', PID=772
[FOUND] Matching process:
  Kernel name: svchost.exe
  Full name: svchost.exe
  PID: 772
  Base: 0x7ff7be6e0000
  CR3: 0x85f957000
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='DeltaForceClie', input='wnbios_poc.exe', PID=14040
system_kprocess: ffff8b0b479dd080
system_cr3: 1aa000
[DEBUG] Checking process: kernel='conhost.exe', input='conhost.exe', PID=2464
[FOUND] Matching process:
  Kernel name: conhost.exe
  Full name: conhost.exe
  PID: 2464
  Base: 0x7ff7e13f0000
  CR3: 0x84698d000