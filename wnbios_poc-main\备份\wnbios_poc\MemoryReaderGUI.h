#pragma once
#include "drv.h"
#include <msclr\marshal_cppstd.h>

using namespace System;
using namespace System::ComponentModel;
using namespace System::Collections;
using namespace System::Collections::Generic;
using namespace System::Windows::Forms;
using namespace System::Data;
using namespace System::Drawing;

// Native structure for LDR_DATA_TABLE_ENTRY
struct LDR_ENTRY
{
    uintptr_t flink;
    uintptr_t blink;
    uintptr_t reserved1[2];
    uintptr_t dllBase;
    uintptr_t entryPoint;
    uint32_t sizeOfImage;
    uint16_t fullDllNameLength;
    uint16_t fullDllNameMaxLength;
    uintptr_t fullDllNameBuffer;
    uint16_t baseDllNameLength;
    uint16_t baseDllNameMaxLength;
    uintptr_t baseDllNameBuffer;
};

// Native structure for UNICODE_STRING
struct UNICODE_STRING_NATIVE
{
    uint16_t Length;
    uint16_t MaximumLength;
    uintptr_t Buffer;
};

// Helper classes for process and module information
ref class ProcessInfo
{
public:
    String^ Name;
    DWORD PID;
    uintptr_t BaseAddress;

    ProcessInfo(String^ name, DWORD pid, uintptr_t baseAddr)
    {
        Name = name;
        PID = pid;
        BaseAddress = baseAddr;
    }

    virtual String^ ToString() override
    {
        return String::Format("{0} (PID: {1})", Name, PID);
    }
};

ref class ModuleInfo
{
public:
    String^ Name;
    uintptr_t BaseAddress;
    uint32_t Size;

    ModuleInfo(String^ name, uintptr_t baseAddr, uint32_t size)
    {
        Name = name;
        BaseAddress = baseAddr;
        Size = size;
    }

    virtual String^ ToString() override
    {
        return String::Format("{0} (Base: 0x{1:X}, Size: 0x{2:X})", Name, BaseAddress, Size);
    }
};

public ref class MemoryReaderForm : public System::Windows::Forms::Form
{
public:
    MemoryReaderForm(void)
    {
        InitializeComponent();
        driver = nullptr;
        allProcesses = gcnew List<ProcessInfo^>();
    }

protected:
    ~MemoryReaderForm()
    {
        if (components)
        {
            delete components;
        }
        if (driver)
        {
            delete driver;
        }
    }

private:
    eneio_lib* driver;
    List<ProcessInfo^>^ allProcesses;
    System::ComponentModel::Container ^components;

    // UI Controls
    System::Windows::Forms::GroupBox^ groupBoxDriver;
    System::Windows::Forms::Button^ btnInitDriver;
    System::Windows::Forms::Label^ lblDriverStatus;

    System::Windows::Forms::GroupBox^ groupBoxProcess;
    System::Windows::Forms::ComboBox^ comboBoxProcesses;
    System::Windows::Forms::Button^ btnRefreshProcesses;
    System::Windows::Forms::Label^ lblProcessInfo;
    System::Windows::Forms::TextBox^ txtProcessFilter;
    System::Windows::Forms::Label^ lblProcessFilter;

    System::Windows::Forms::GroupBox^ groupBoxModules;
    System::Windows::Forms::ListBox^ listBoxModules;
    System::Windows::Forms::Button^ btnRefreshModules;

    System::Windows::Forms::GroupBox^ groupBoxMemory;
    System::Windows::Forms::TextBox^ txtAddress;
    System::Windows::Forms::NumericUpDown^ numSize;
    System::Windows::Forms::Button^ btnReadMemory;
    System::Windows::Forms::Label^ lblAddress;
    System::Windows::Forms::Label^ lblSize;

    System::Windows::Forms::GroupBox^ groupBoxOutput;
    System::Windows::Forms::ComboBox^ comboBoxDataType;
    System::Windows::Forms::TextBox^ txtOutput;
    System::Windows::Forms::Button^ btnCopyOutput;

    System::Windows::Forms::StatusStrip^ statusStrip;
    System::Windows::Forms::ToolStripStatusLabel^ statusLabel;

    void InitializeComponent(void);

    // Event handlers
    System::Void btnInitDriver_Click(System::Object^ sender, System::EventArgs^ e);
    System::Void btnRefreshProcesses_Click(System::Object^ sender, System::EventArgs^ e);
    System::Void btnRefreshModules_Click(System::Object^ sender, System::EventArgs^ e);
    System::Void btnReadMemory_Click(System::Object^ sender, System::EventArgs^ e);
    System::Void btnCopyOutput_Click(System::Object^ sender, System::EventArgs^ e);
    System::Void comboBoxProcesses_SelectedIndexChanged(System::Object^ sender, System::EventArgs^ e);
    System::Void listBoxModules_SelectedIndexChanged(System::Object^ sender, System::EventArgs^ e);
    System::Void txtProcessFilter_TextChanged(System::Object^ sender, System::EventArgs^ e);

    // Helper methods
    void UpdateDriverStatus(bool initialized);
    void RefreshProcessList();
    void RefreshModuleList();
    void ReadAndDisplayMemory();
    String^ FormatDataAsType(array<System::Byte>^ data, String^ dataType);
    String^ ByteArrayToHexString(array<System::Byte>^ data);
    void SetStatus(String^ message);
    void EnumerateModulesFromPEB(uintptr_t pebAddr, String^ processName);
    void ApplyProcessFilter();
};